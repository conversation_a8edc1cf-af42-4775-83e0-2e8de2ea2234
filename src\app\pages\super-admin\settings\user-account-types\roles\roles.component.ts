import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  showPermissionsModal: boolean = false;
  allPermissions: any[] = [];

  constructor(private router: Router, private settingService: settingService ) { }

  ngOnInit(): void {
    this.lodingAllRoles();
    this.loadAllPermissions();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        this.roles = response.data || [];
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
      }
    });
  }

  viewPermissions(id: number): void {
    const role = this.roles.find(r => r.id === id);
    if (role) {
      this.selectedRole = { ...role };
       this.allPermissions = this.allPermissions.map(permission => ({
        ...permission,
        isSelected: this.selectedRole.permissions?.includes(permission.id) || false
      }));
      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    return this.allPermissions?.every(permission => permission.isSelected) || false;
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    this.allPermissions?.forEach(permission => permission.isSelected = isChecked);
  }

  loadAllPermissions() {
    this.settingService.getAllPermissions().subscribe({
      next: (response) => {
        this.allPermissions = response.data || [];
       this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading permissions:', error);
      }
    });
  }

  savePermissions(): void {
     const selectedPermissions = this.allPermissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.name);

    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
         const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = selectedPermissions;
        }

        Swal.fire('Permissions updated successfully!').then(() => {
          this.closePermissionsModal();

          window.location.reload();
        });
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
  }

}
