import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;
  allPermissions: any[] = [];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
    this.loadAllPermissions();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.roles = response.data || [];


        // this.roles = this.roles.map(role => ({
        //   ...role,
        //   permissions: role.permissions ? role.permissions.map((permission: any) => {
        //     // If permission is already an object, keep it as is
        //     if (typeof permission === 'object' && permission.name) {
        //       return {
        //         ...permission,
        //         isSelected: permission.isSelected !== undefined ? permission.isSelected : true
        //       };
        //     }
        //     // If permission is a string, convert it to object
        //     else if (typeof permission === 'string') {
        //       return {
        //         id: permission,
        //         name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        //         description: `Permission to ${permission.replace(/_/g, ' ')}`,
        //         isSelected: true
        //       };
        //     }
        //     return permission;
        //   }) : []
        // }));

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }




  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Initialize permissions if they don't exist
      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }

      // Prepare allPermissions with isSelected status based on role's permissions
      this.allPermissions = this.allPermissions.map(permission => {
        // Check if this permission exists in the role's permissions
        const roleHasPermission = this.selectedRole.permissions.some((rolePermission: any) => {
          // Handle both string and object permission formats
          const permissionId = typeof rolePermission === 'string' ? rolePermission : rolePermission.id || rolePermission.name;
          return permissionId === permission.id || permissionId === permission.name;
        });

        return {
          ...permission,
          isSelected: roleHasPermission // Only check if permission exists in role's permissions
        };
      });

      // Store original permissions for comparison
      this.originalPermissions = JSON.parse(JSON.stringify(this.allPermissions));

      // Show the modal (simple way)
      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.allPermissions || this.allPermissions.length === 0) {
      return false;
    }
    return this.allPermissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.allPermissions) {
      this.allPermissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {
    this.cd.detectChanges();
  }

loadAllPermissions() {
  this.settingService.getAllPermissions().subscribe({
    next: (response) => {
      this.allPermissions = response.data ;
      console.log("aaaaaa",this.allPermissions)
      this.cd.detectChanges();
    },
    error: (error) => {
      console.error('Error loading permissions:', error);
    }
  });
}

  savePermissions(): void {
    // Get selected permissions from allPermissions array
    const selectedPermissions = this.allPermissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.id || permission.name);

    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
        // Update local data - update the role's permissions with selected ones
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = this.allPermissions
            .filter(p => p.isSelected)
            .map(p => ({ id: p.id, name: p.name, isSelected: true }));
        }

        this.closePermissionsModal();
        Swal.fire('Permissions updated successfully!');
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
    // Reset allPermissions isSelected status
    this.allPermissions = this.allPermissions.map(permission => ({
      ...permission,
      isSelected: false
    }));
  }

}
